from enum import Enum

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    TDIDF = "tfidf"
    BM25 = "bm25"


    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return """Grep: use command to match the keywords in code snippet, the query input should be specific keywords and searchable。
- Expected Query
1. keyword that likely appear in code snippets, like function, class, api name, etc 
2. keyword can be directly found in the code or documents
3. Just only one word

- Examples
Original Query: "解释这个存储级别的仓库的主要功能"
Output: ["Repository", "Storage", "Store", "Save", "Load", "Fetch", "Persist", "Read", "Write", "DataSource"]
"""
        elif self == SearchToolEnum.EMBEDDING:
            return "embedding: use vector database to search code, the query input should be general and cover all possible answers"
        elif self == SearchToolEnum.BM25:
            return """BM25: use BM25 algorithm to search code, the query input could be the code snippet likely to appear in the repository
- Examples
Original Query: "解释这个存储级别的仓库的主要功能"

- Expected Query
1. the classic code snippet reletive to the key topics of the query
2. Each code snippet not exceeding 200 words
3. Must in English

Output: ["public interface ProductRepository {
  Product findById(Long id);
  List<Product> findAll();
  void save(Product product);
  void delete(Long id);
  List<Product> findByCategory(String category);
}", "@Repository
public class JpaProductRepository implements ProductRepository {
  @PersistenceContext
  private EntityManager em;
  
  @Override
  public Product findById(Long id) {
    return em.find(Product.class, id);
  }
  ...
}", "@Transactional
public void transferFunds(Long fromId, Long toId, BigDecimal amount) {
  Account from = accountRepository.findById(fromId);
  Account to = accountRepository.findById(toId);
  from.debit(amount);
  to.credit(amount);
  accountRepository.update(from);
  accountRepository.update(to);
}"]
"""
        else:
            return "未知搜索工具"

    @property
    def search_class(self):
        if self == SearchToolEnum.GREP:
            from modules.integration.tools.grep_search import GrepSearchTool
            return GrepSearchTool
        elif self == SearchToolEnum.EMBEDDING:
            raise NotImplementedError("Embedding搜索暂未实现")
            # from modules.integration.embedding import EmbeddingSearchTool
            # return EmbeddingSearchTool
        elif self == SearchToolEnum.BM25:
            from modules.integration.tools.bm25_search import BM25Search
            return BM25Search
        else:
            raise ValueError("未知搜索工具")

class SuffixLanguage(Enum):
    JAVA = "java"
    PYTHON = "python"
    TEXT = "text"
