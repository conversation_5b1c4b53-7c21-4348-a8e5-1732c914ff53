import os
import json
import math
from hashlib import sha256
from typing import List
import rich
from collections import Counter
from functools import lru_cache

from core.config import get_config
from modules.common.schema import CodeSnippet
from modules.common.search_tool import SearchToolABC
from modules.chunks.IChunk import IChunk
from modules.chunks.chunk_factory import getChunkService
from utils.file import build_file_list
from utils.term import get_terms
from utils.logger import logger

class BM25Search(SearchToolABC):
    def __init__(self, repo_path: str, cache_dir: str = get_config().data.cache_dir):
        self.repo_path = repo_path

        self.k1 = 1.2
        self.b = 0.75

        self.project_name = None

        self.chunk_splitter: IChunk = getChunkService(get_config().chunk.name)()

        self.chunk_content = None
        self.term_idf = None
        self.chunks_term_freqs = None
        self.chunk_lengths = None
        self.chunk_doc_length = None

        self.load_context_files(repo_path, cache_dir, sha256(self.repo_path.encode()).hexdigest()[:32])

    @lru_cache()
    def load_cache_file(self, cache_file: str):
        with open(cache_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def load_context_files(self, project_dir: str, cache_dir: str, project_id: str):
        """
        加载上下文文件并构建BM25索引
        """
        bm25_cache_dir = os.path.join(cache_dir, "bm25")
        cache_file = os.path.join(bm25_cache_dir, f"{project_id}.json")

        # 如果缓存文件存在，直接加载
        if os.path.exists(cache_file):
            cache_data = self.load_cache_file(cache_file)
            self.chunk_content = cache_data['chunk_content']
            self.term_idf = cache_data['term_idf']
            self.chunks_term_freqs = cache_data['chunk_term_freqs']
            self.chunk_lengths = cache_data['chunk_lengths']
            self.chunk_doc_length = cache_data['avg_chunk_length']
            return {"status": "loaded_from_cache", "documents": len(self.chunk_content)}

        # 确保缓存目录存在
        os.makedirs(bm25_cache_dir, exist_ok=True)

        # 读取项目目录中的文件
        doc_contents = build_file_list(project_dir)

        # 代码切分
        self.chunk_content = {}
        for doc_path, content in rich.progress.track(doc_contents.items(), description="Chunking files..."):
            for line_scope, content in self.chunk_splitter.chunk_file(content).items():
                self.chunk_content[f"{doc_path}:{line_scope}"] = content

        # 计算所有chunk中的term和IDF值
        all_terms = set()
        chunk_terms = {}
        doc_freqs = {}
        for chunk_path, content in rich.progress.track(self.chunk_content.items(), description="Processing files..."):
            terms = get_terms(content)
            chunk_terms[chunk_path] = terms
            all_terms.update(terms)
            for term in terms:
                doc_freqs[term] = doc_freqs.get(term, 0) + 1

        # 计算每个term的IDF值
        total_docs = len(self.chunk_content)
        self.term_idf = {}

        for term in rich.progress.track(all_terms, description="Calculating IDF..."):
            doc_freq = doc_freqs.get(term, 0)
            # IDF计算：log(N/df)，其中N是总文档数，df是包含该term的文档数
            self.term_idf[term] = math.log(total_docs / doc_freq) if doc_freq > 0 else 0

        # 计算每个文件内每个term的频率
        self.chunks_term_freqs = {}
        for chunk_path, terms in rich.progress.track(chunk_terms.items(), description="Calculating term frequencies..."):
            term_counts = Counter(terms)
            self.chunks_term_freqs[chunk_path] = dict(term_counts)


        # 计算平均文档长度
        self.chunk_lengths = {chunk_path: sum(freqs.values())
                      for chunk_path, freqs in rich.progress.track(self.chunks_term_freqs.items(), description="Calculating document lengths...")}
        self.chunk_doc_length = sum(self.chunk_lengths.values()) / len(self.chunk_lengths) if self.chunk_lengths else 1

        # 缓存结果
        cache_data = {
            'chunk_content': self.chunk_content,
            'term_idf': self.term_idf,
            'chunk_term_freqs': self.chunks_term_freqs,
            'chunk_lengths': self.chunk_lengths,
            'avg_chunk_length': self.chunk_doc_length
        }

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

        return {
            "status": "indexed",
            "chunks": len(self.chunk_content),
            "unique_terms": len(self.term_idf)
        }

    def search(self, query: str, top_k: int = get_config().deepsearch.max_new_queries) -> List[CodeSnippet]:
        if not self.chunk_content or not self.term_idf or not self.chunks_term_freqs:
            return {}

        # 提取查询词
        query_terms = get_terms(query)
        if not query_terms:
            return {}
        
        # 计算每个文档的BM25分数
        scores = {}
        for chunk_path in self.chunk_content:
            score = self._calculate_bm25_score(
                query_terms, chunk_path, self.chunk_lengths[chunk_path], self.chunk_doc_length
            )
            if score > 0:
                scores[chunk_path] = score

        logger.info(f"BM25 Search Scores: {scores}")

        # 按分数排序并返回top_k结果
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return [CodeSnippet(
            file_path=chunk_path.split(":")[0],
            start_line=int(chunk_path.split(":")[1].split("-")[0]),
            end_line=int(chunk_path.split(":")[1].split("-")[1]),
            content=self.chunk_content[chunk_path],
            context_before="",
            context_after=""
        ) for chunk_path, _ in sorted_scores[:top_k]]

    def _calculate_bm25_score(self, query_terms: List[str], file_path: str,
                             doc_length: int, avg_doc_length: float) -> float:
        """
        计算单个文档的BM25分数
        """
        if file_path not in self.chunks_term_freqs:
            return 0.0

        term_freqs = self.chunks_term_freqs[file_path]
        score = 0.0

        for term in query_terms:
            if term in term_freqs and term in self.term_idf:
                # 词频
                tf = term_freqs[term]
                # IDF值
                idf = self.term_idf[term]

                # BM25公式
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / avg_doc_length))

                score += idf * (numerator / denominator)

        return score